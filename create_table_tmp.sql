DECLARE
    -------------------------------------------------执行上下文信息------------------------------------------------------
    v_log_msg text;
    v_run_sp_name text;
    v_run_pg_context text;
    v_run_state   text;
    v_run_msg     text;
    v_run_exception_detail  text;
    v_run_exception_hint    text;
    v_run_exception_context text;
    -------------------------------------------------------------------------------------------------------------------
    v_exist int;
    v_sql text;
    v_part_range_start varchar(8);
    v_part_range_end varchar(8);
    v_partition_name varchar(64);
    -- 新增变量
    v_year_month varchar(6);
    v_table_name varchar(64);
    v_create_table_sql text;
    v_year int;
    v_month int;
BEGIN
    GET DIAGNOSTICS v_run_pg_context = PG_CONTEXT;
    v_run_sp_name = public.f_get_fx_name('dw', v_run_pg_context);
    
    -- 开始
    perform public.sp_log(v_run_sp_name, 'START', '开始计算', 'INFO');
   
    -- 动态建表SQL模板
    v_create_table_sql := '
    CREATE TABLE IF NOT EXISTS dw.dw_asset_cust_maxbal_pt_%s (
        trade_dt date,
        cust_no character varying(10),
        balance_vol numeric,
        market_amt numeric,
        amt_0 numeric,
        amt_1 numeric,
        amt_2 numeric,
        amt_3 numeric,
        amt_4 numeric,
        amt_5 numeric,
        amt_6 numeric,
        amt_7 numeric,
        amt_8 numeric,
        amt_bxdq numeric,
        amt_bxhqj numeric,
        amt_cxcp numeric,
        amt_cxg numeric,
        amt_pt numeric,
        amt_fhb numeric,
        pop_time timestamp without time zone
    ) DISTRIBUTED BY (cust_no);
    
    COMMENT ON TABLE dw.dw_asset_cust_maxbal_pt_%s IS ''dw_客户历史持有最大普通公募市值统计_%s'';
    COMMENT ON COLUMN dw.dw_asset_cust_maxbal_pt_%s.trade_dt IS ''统计日'';
    COMMENT ON COLUMN dw.dw_asset_cust_maxbal_pt_%s.cust_no IS ''客户号'';
    COMMENT ON COLUMN dw.dw_asset_cust_maxbal_pt_%s.balance_vol IS ''总份额'';
    COMMENT ON COLUMN dw.dw_asset_cust_maxbal_pt_%s.market_amt IS ''总市值'';
    COMMENT ON COLUMN dw.dw_asset_cust_maxbal_pt_%s.amt_0 IS ''市值_股票型'';
    COMMENT ON COLUMN dw.dw_asset_cust_maxbal_pt_%s.amt_1 IS ''市值_混合型'';
    COMMENT ON COLUMN dw.dw_asset_cust_maxbal_pt_%s.amt_2 IS ''市值_债券型'';
    COMMENT ON COLUMN dw.dw_asset_cust_maxbal_pt_%s.amt_3 IS ''市值_货币型'';
    COMMENT ON COLUMN dw.dw_asset_cust_maxbal_pt_%s.amt_4 IS ''市值_QDII'';
    COMMENT ON COLUMN dw.dw_asset_cust_maxbal_pt_%s.amt_5 IS ''市值_封闭式'';
    COMMENT ON COLUMN dw.dw_asset_cust_maxbal_pt_%s.amt_6 IS ''市值_结构型'';
    COMMENT ON COLUMN dw.dw_asset_cust_maxbal_pt_%s.amt_7 IS ''市值_公募专户'';
    COMMENT ON COLUMN dw.dw_asset_cust_maxbal_pt_%s.amt_8 IS ''市值_券商集合'';
    COMMENT ON COLUMN dw.dw_asset_cust_maxbal_pt_%s.amt_bxdq IS ''市值_保险定期'';
    COMMENT ON COLUMN dw.dw_asset_cust_maxbal_pt_%s.amt_bxhqj IS ''市值_保险活期+'';
    COMMENT ON COLUMN dw.dw_asset_cust_maxbal_pt_%s.amt_cxcp IS ''市值_创新产品'';
    COMMENT ON COLUMN dw.dw_asset_cust_maxbal_pt_%s.amt_cxg IS ''市值_储蓄罐'';
    COMMENT ON COLUMN dw.dw_asset_cust_maxbal_pt_%s.amt_pt IS ''市值_普通公募'';
    COMMENT ON COLUMN dw.dw_asset_cust_maxbal_pt_%s.amt_fhb IS ''市值_非货币'';
    COMMENT ON COLUMN dw.dw_asset_cust_maxbal_pt_%s.pop_time IS ''数据生成时间'';
    ';

    -- 循环创建从202001到202209的表
    FOR v_year IN 2020..2022 LOOP
        FOR v_month IN 1..12 LOOP
            -- 生成年月字符串 (YYYYMM格式)
            v_year_month := v_year::text || lpad(v_month::text, 2, '0');
            
            -- 只处理202001到202209的范围
            IF v_year_month >= '202001' AND v_year_month <= '202209' THEN
                v_table_name := 'dw_asset_cust_maxbal_pt_' || v_year_month;
                
                -- 生成具体的建表SQL
                v_sql := format(v_create_table_sql, 
                    v_year_month, v_year_month, v_year_month, v_year_month, v_year_month,
                    v_year_month, v_year_month, v_year_month, v_year_month, v_year_month,
                    v_year_month, v_year_month, v_year_month, v_year_month, v_year_month,
                    v_year_month, v_year_month, v_year_month, v_year_month, v_year_month,
                    v_year_month, v_year_month);
                
                -- 执行建表SQL
                EXECUTE v_sql;
                
                -- 记录日志
                perform public.sp_log(v_run_sp_name, 'INFO', '创建表: ' || v_table_name, 'INFO');
            END IF;
        END LOOP;
    END LOOP;

    -- 结束
    perform public.sp_log(v_run_sp_name, 'END', '月份202001-202209数据表创建结束', 'INFO');
    po_retcode := '0';
    po_retmsg := v_run_sp_name ||' ~ finished';