#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SQL Schema前缀添加工具
用于给SQL语句中的表名添加schema前缀
"""

import re
import sys
import os

def add_schema_prefix_to_sql(sql_content, schema_name):
    """
    给SQL语句中的表名添加schema前缀
    
    Args:
        sql_content (str): 原始SQL内容
        schema_name (str): 要添加的schema名称
    
    Returns:
        str: 添加了schema前缀的SQL内容
    """
    
    # 定义需要添加前缀的表名列表
    table_names = [
        'CM_TRADE_NUM_SUMMARY',
        'CM_PRP_PREID_EXP_COEFF', 
        'CM_PREBOOKPRODUCTINFO',
        'CM_PREBOOK_MANYCALL',
        'CM_DISCOUNTAPP',
        'CM_CONSULTANT',
        'CM_PRP_PRODUCT_COEFFICIENT',
        'CM_PRP_CUST_SOURCE_COEFF',
        'CM_PRP_SOURCE_COEFFICIENT',
        'JJXX1',
        'RMBHLZJJ',
        'CM_PRP_ORDERINFO_TYPE_COEFF',
        'CM_PRP_TRADE_NUM'
    ]
    
    # 创建修改后的SQL内容
    modified_sql = sql_content
    
    # 为每个表名添加schema前缀
    for table_name in table_names:
        # 使用正则表达式匹配表名，确保不会误替换字段名或其他内容
        # 匹配模式：表名前面是空格、FROM、JOIN、逗号等，后面是空格或别名
        patterns = [
            # FROM 表名
            rf'\bFROM\s+{re.escape(table_name)}\b',
            # JOIN 表名  
            rf'\bJOIN\s+{re.escape(table_name)}\b',
            # 子查询中的表名
            rf'\b{re.escape(table_name)}\s+(?=[A-Z][A-Z0-9]*\b)',  # 表名后跟别名
            rf'\b{re.escape(table_name)}\s+(?=WHERE|ON|GROUP|ORDER|HAVING)',  # 表名后跟关键字
        ]
        
        for pattern in patterns:
            # 替换时保持原有的格式，只在表名前添加schema
            def replace_func(match):
                matched_text = match.group(0)
                return matched_text.replace(table_name, f"{schema_name}.{table_name}")
            
            modified_sql = re.sub(pattern, replace_func, modified_sql, flags=re.IGNORECASE)
    
    # 特殊处理：直接替换没有被上述模式匹配到的表名
    # 这个更通用的替换，但需要小心不要替换字段名
    for table_name in table_names:
        # 查找所有独立的表名（前后有单词边界）
        pattern = rf'\b{re.escape(table_name)}\b'
        
        def careful_replace(match):
            # 获取匹配的上下文
            start = max(0, match.start() - 20)
            end = min(len(modified_sql), match.end() + 20)
            context = modified_sql[start:end].upper()
            
            # 如果上下文中包含FROM、JOIN等关键字，且表名前没有schema前缀
            if any(keyword in context for keyword in ['FROM', 'JOIN']) and f"{schema_name.upper()}." not in context:
                # 检查表名前是否已经有schema前缀
                before_match = modified_sql[max(0, match.start()-50):match.start()]
                if f"{schema_name}." not in before_match[-20:]:
                    return f"{schema_name}.{match.group(0)}"
            
            return match.group(0)
        
        modified_sql = re.sub(pattern, careful_replace, modified_sql, flags=re.IGNORECASE)
    
    return modified_sql

def process_sql_file(input_file, output_file, schema_name):
    """
    处理SQL文件，添加schema前缀
    
    Args:
        input_file (str): 输入文件路径
        output_file (str): 输出文件路径  
        schema_name (str): schema名称
    """
    try:
        # 读取原始SQL文件
        with open(input_file, 'r', encoding='utf-8') as f:
            sql_content = f.read()
        
        print(f"正在处理文件: {input_file}")
        print(f"Schema前缀: {schema_name}")
        
        # 添加schema前缀
        modified_sql = add_schema_prefix_to_sql(sql_content, schema_name)
        
        # 写入修改后的SQL文件
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(modified_sql)
        
        print(f"处理完成，输出文件: {output_file}")
        
        # 显示修改统计
        original_lines = sql_content.count('\n') + 1
        modified_lines = modified_sql.count('\n') + 1
        print(f"原始行数: {original_lines}")
        print(f"修改后行数: {modified_lines}")
        
    except Exception as e:
        print(f"处理文件时出错: {str(e)}")

def main():
    """主函数"""
    if len(sys.argv) < 3:
        print("用法:")
        print("  python add_schema_prefix.py <schema_name> <input_file> [output_file]")
        print("")
        print("示例:")
        print("  python add_schema_prefix.py docker_it34_cust original.sql modified.sql")
        print("  python add_schema_prefix.py docker_it34_cust original.sql")
        print("")
        print("如果不指定输出文件，将在输入文件名后添加'_with_schema'后缀")
        return
    
    schema_name = sys.argv[1]
    input_file = sys.argv[2]
    
    # 确定输出文件名
    if len(sys.argv) > 3:
        output_file = sys.argv[3]
    else:
        # 自动生成输出文件名
        name, ext = os.path.splitext(input_file)
        output_file = f"{name}_with_schema{ext}"
    
    # 检查输入文件是否存在
    if not os.path.exists(input_file):
        print(f"错误: 输入文件 '{input_file}' 不存在")
        return
    
    # 处理文件
    process_sql_file(input_file, output_file, schema_name)

if __name__ == "__main__":
    main()
