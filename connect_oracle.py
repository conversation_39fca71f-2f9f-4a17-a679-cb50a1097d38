import cx_Oracle
from datetime import datetime
import sys
import os

def connect_to_oracle():
    """连接到Oracle数据库并返回连接对象"""
    try:
        # Oracle数据库连接参数
        host = "***************"
        port = "1521"
        service_name = "hbcrm"
        username = "docker_cust_dev"
        password = "cust1"
        
        # 构建连接字符串
        dsn = cx_Oracle.makedsn(host, port, service_name=service_name)
        conn_string = f"{username}/{password}@{dsn}"
        
        print(f"正在连接Oracle数据库: {host}:{port}/{service_name}")
        
        # 连接数据库
        conn = cx_Oracle.connect(conn_string)
        print("Oracle数据库连接成功!")
        return conn
    except Exception as e:
        print(f"Oracle数据库连接失败: {str(e)}")
        print("请确保:")
        print("1. Oracle Instant Client已安装")
        print("2. 网络连接正常")
        print("3. 数据库服务正在运行")
        print("4. 用户名密码正确")
        return None

def test_oracle_connection():
    """测试Oracle数据库连接并执行简单查询"""
    conn = connect_to_oracle()
    if conn:
        try:
            # 创建游标
            cur = conn.cursor()
            
            # 执行简单查询
            cur.execute("SELECT SYSDATE, USER, SYS_CONTEXT('USERENV','DB_NAME') FROM DUAL")
            
            # 获取结果
            result = cur.fetchone()
            print("\nOracle数据库连接信息:")
            print(f"当前时间: {result[0]}")
            print(f"当前用户: {result[1]}")
            print(f"数据库名: {result[2]}")
            
            # 关闭游标和连接
            cur.close()
            conn.close()
            print("Oracle数据库连接已关闭")
        except Exception as e:
            print(f"查询执行失败: {str(e)}")

def check_oracle_tables():
    """检查Oracle数据库中SQL涉及的表是否存在"""
    # 从SQL中提取的表名列表
    tables_to_check = [
        'CM_TRADE_NUM_SUMMARY',
        'CM_PRP_PREID_EXP_COEFF', 
        'CM_PREBOOKPRODUCTINFO',
        'CM_PREBOOK_MANYCALL',
        'CM_DISCOUNTAPP',
        'CM_CONSULTANT',
        'CM_PRP_PRODUCT_COEFFICIENT',
        'CM_PRP_CUST_SOURCE_COEFF',
        'CM_PRP_SOURCE_COEFFICIENT',
        'JJXX1',
        'RMBHLZJJ',
        'CM_PRP_ORDERINFO_TYPE_COEFF',
        'CM_PRP_TRADE_NUM'
    ]
    
    conn = connect_to_oracle()
    if conn:
        try:
            cur = conn.cursor()
            
            print("\n检查Oracle表/视图是否存在:")
            print("=" * 60)
            
            missing_tables = []
            existing_tables = []
            
            for table_name in tables_to_check:
                try:
                    # 检查表是否存在 (Oracle语法)
                    cur.execute("""
                        SELECT COUNT(*) 
                        FROM USER_TABLES 
                        WHERE TABLE_NAME = UPPER(:table_name)
                    """, {'table_name': table_name})
                    
                    table_count = cur.fetchone()[0]
                    
                    if table_count > 0:
                        existing_tables.append(table_name)
                        print(f"✅ {table_name} - 存在 (表)")
                    else:
                        # 检查是否是视图
                        cur.execute("""
                            SELECT COUNT(*) 
                            FROM USER_VIEWS 
                            WHERE VIEW_NAME = UPPER(:table_name)
                        """, {'table_name': table_name})
                        
                        view_count = cur.fetchone()[0]
                        if view_count > 0:
                            existing_tables.append(table_name)
                            print(f"✅ {table_name} - 存在 (视图)")
                        else:
                            # 检查是否是同义词
                            cur.execute("""
                                SELECT COUNT(*) 
                                FROM USER_SYNONYMS 
                                WHERE SYNONYM_NAME = UPPER(:table_name)
                            """, {'table_name': table_name})
                            
                            synonym_count = cur.fetchone()[0]
                            if synonym_count > 0:
                                existing_tables.append(table_name)
                                print(f"✅ {table_name} - 存在 (同义词)")
                            else:
                                missing_tables.append(table_name)
                                print(f"❌ {table_name} - 不存在")
                                
                except Exception as e:
                    missing_tables.append(table_name)
                    print(f"❌ {table_name} - 检查失败: {str(e)}")
            
            print("\n" + "=" * 60)
            print(f"总计: {len(existing_tables)} 个存在, {len(missing_tables)} 个不存在")
            
            if missing_tables:
                print(f"\n❌ 缺失的表/视图:")
                for table in missing_tables:
                    print(f"   - {table}")
                    
            if existing_tables:
                print(f"\n✅ 存在的表/视图:")
                for table in existing_tables:
                    print(f"   - {table}")
            
            cur.close()
            conn.close()
            
        except Exception as e:
            print(f"检查表失败: {str(e)}")
            if conn:
                conn.close()

def execute_oracle_sql(sql_text):
    """执行Oracle SQL语句"""
    conn = connect_to_oracle()
    if conn:
        try:
            cur = conn.cursor()
            
            print("正在执行SQL查询...")
            print("SQL预览:")
            print(sql_text[:200] + "..." if len(sql_text) > 200 else sql_text)
            
            # 执行SQL
            cur.execute(sql_text)
            
            # 获取结果
            results = cur.fetchall()
            
            print(f"\n查询成功! 返回 {len(results)} 行数据")
            
            # 显示前几行结果
            if results:
                print("\n前5行结果:")
                for i, row in enumerate(results[:5]):
                    print(f"第{i+1}行: {row}")
            
            cur.close()
            conn.close()
            
        except Exception as e:
            print(f"SQL执行失败: {str(e)}")
            import traceback
            print("详细错误信息:")
            print(traceback.format_exc())
            if conn:
                conn.close()

def get_table_info(table_name):
    """获取表的详细信息"""
    conn = connect_to_oracle()
    if conn:
        try:
            cur = conn.cursor()
            
            print(f"\n获取表 {table_name} 的信息:")
            print("=" * 50)
            
            # 获取表结构
            cur.execute("""
                SELECT COLUMN_NAME, DATA_TYPE, DATA_LENGTH, NULLABLE, DATA_DEFAULT
                FROM USER_TAB_COLUMNS 
                WHERE TABLE_NAME = UPPER(:table_name)
                ORDER BY COLUMN_ID
            """, {'table_name': table_name})
            
            columns = cur.fetchall()
            
            if columns:
                print(f"表结构 ({len(columns)} 个字段):")
                print("-" * 80)
                print(f"{'字段名':<30} {'类型':<15} {'长度':<8} {'可空':<6} {'默认值':<15}")
                print("-" * 80)
                
                for col in columns:
                    col_name, data_type, data_length, nullable, default_val = col
                    length_str = str(data_length) if data_length else ""
                    nullable_str = "YES" if nullable == 'Y' else "NO"
                    default_str = str(default_val)[:15] if default_val else ""
                    
                    print(f"{col_name:<30} {data_type:<15} {length_str:<8} {nullable_str:<6} {default_str:<15}")
            else:
                print(f"表 {table_name} 不存在或无权限访问")
            
            cur.close()
            conn.close()
            
        except Exception as e:
            print(f"获取表信息失败: {str(e)}")
            if conn:
                conn.close()

if __name__ == "__main__":
    if len(sys.argv) > 1:
        command = sys.argv[1]
        
        if command == "test":
            # 测试连接
            test_oracle_connection()
        elif command == "check":
            # 检查表是否存在
            check_oracle_tables()
        elif command == "info" and len(sys.argv) > 2:
            # 获取表信息
            table_name = sys.argv[2]
            get_table_info(table_name)
        elif command == "sql" and len(sys.argv) > 2:
            # 执行SQL
            sql_file = sys.argv[2]
            if os.path.exists(sql_file):
                with open(sql_file, 'r', encoding='utf-8') as f:
                    sql_content = f.read()
                execute_oracle_sql(sql_content)
            else:
                print(f"SQL文件 {sql_file} 不存在")
        else:
            print("用法:")
            print("  python connect_oracle.py test          # 测试连接")
            print("  python connect_oracle.py check         # 检查表是否存在")
            print("  python connect_oracle.py info <表名>    # 获取表信息")
            print("  python connect_oracle.py sql <文件名>   # 执行SQL文件")
    else:
        # 默认测试连接
        test_oracle_connection()
