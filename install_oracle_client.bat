@echo off
echo 安装Oracle客户端和cx_Oracle库...
echo.

echo 1. 安装cx_Oracle Python库...
pip install cx_Oracle

echo.
echo 2. 检查Oracle Instant Client...
echo.

echo Oracle Instant Client安装说明:
echo ================================
echo.
echo 如果您还没有安装Oracle Instant Client，请按以下步骤操作:
echo.
echo 1. 访问Oracle官网下载页面:
echo    https://www.oracle.com/database/technologies/instant-client/downloads.html
echo.
echo 2. 下载适合您系统的Oracle Instant Client Basic包
echo    - Windows x64: instantclient-basic-windows.x64-21.x.x.x.zip
echo.
echo 3. 解压到目录，例如: C:\oracle\instantclient_21_x
echo.
echo 4. 将解压目录添加到系统PATH环境变量中
echo.
echo 5. 或者设置环境变量:
echo    set PATH=C:\oracle\instantclient_21_x;%%PATH%%
echo.

echo 正在检查cx_Oracle是否安装成功...
python -c "import cx_Oracle; print('cx_Oracle版本:', cx_Oracle.version)"

if %errorlevel% equ 0 (
    echo.
    echo ✅ cx_Oracle安装成功!
    echo 现在可以运行: python connect_oracle.py test
) else (
    echo.
    echo ❌ cx_Oracle安装失败，请检查Oracle Instant Client是否正确安装
)

echo.
pause
