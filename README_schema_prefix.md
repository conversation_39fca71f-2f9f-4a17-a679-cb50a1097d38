# SQL Schema前缀添加工具

## 概述
本工具用于给Oracle SQL语句中的表名添加schema前缀，解决跨schema访问表的问题。

## 文件说明

### 1. `sql_with_schema_prefix.sql`
- **描述**: 已添加`docker_it34_cust.`前缀的完整SQL文件
- **用途**: 可直接在Oracle数据库中执行
- **修改内容**: 所有表名前都添加了`docker_it34_cust.`前缀

### 2. `add_schema_prefix.py`  
- **描述**: 自动化工具，用于给任意SQL文件添加schema前缀
- **功能**: 
  - 智能识别SQL中的表名
  - 自动添加指定的schema前缀
  - 避免误替换字段名或其他内容

### 3. `connect_oracle.py`
- **描述**: Oracle数据库连接工具
- **功能**:
  - 连接Oracle数据库
  - 检查表是否存在
  - 执行SQL查询
  - 获取表结构信息

### 4. `install_oracle_client.bat`
- **描述**: Oracle客户端安装脚本
- **功能**: 安装cx_Oracle库和Oracle Instant Client

## 使用方法

### 方法1: 直接使用修改后的SQL
```sql
-- 直接使用 sql_with_schema_prefix.sql 文件
-- 该文件已包含所有必要的schema前缀
```

### 方法2: 使用自动化工具
```bash
# 给SQL文件添加schema前缀
python add_schema_prefix.py docker_it34_cust original.sql modified.sql

# 或者使用默认输出文件名
python add_schema_prefix.py docker_it34_cust original.sql
```

### 方法3: 使用Oracle连接工具
```bash
# 测试Oracle连接
python connect_oracle.py test

# 检查表是否存在
python connect_oracle.py check

# 获取表结构信息
python connect_oracle.py info CM_TRADE_NUM_SUMMARY

# 执行SQL文件
python connect_oracle.py sql sql_with_schema_prefix.sql
```

## 涉及的表名

以下表名已添加`docker_it34_cust.`前缀:

1. `CM_TRADE_NUM_SUMMARY`
2. `CM_PRP_PREID_EXP_COEFF`
3. `CM_PREBOOKPRODUCTINFO`
4. `CM_PREBOOK_MANYCALL`
5. `CM_DISCOUNTAPP`
6. `CM_CONSULTANT`
7. `CM_PRP_PRODUCT_COEFFICIENT`
8. `CM_PRP_CUST_SOURCE_COEFF`
9. `CM_PRP_SOURCE_COEFFICIENT`
10. `JJXX1`
11. `RMBHLZJJ`
12. `CM_PRP_ORDERINFO_TYPE_COEFF`
13. `CM_PRP_TRADE_NUM`

## 数据库连接信息

```
主机: 192.168.220.170
端口: 1521
数据库: hbcrm
用户名: docker_cust_dev
密码: cust1
Schema: docker_it34_cust
```

## 修改前后对比

### 修改前:
```sql
FROM CM_TRADE_NUM_SUMMARY T
LEFT JOIN CM_PRP_PREID_EXP_COEFF T1 ON ...
```

### 修改后:
```sql
FROM docker_it34_cust.CM_TRADE_NUM_SUMMARY T
LEFT JOIN docker_it34_cust.CM_PRP_PREID_EXP_COEFF T1 ON ...
```

## 注意事项

1. **字符编码**: 确保SQL文件使用UTF-8编码
2. **权限检查**: 确认用户对目标schema有访问权限
3. **表存在性**: 建议先使用`connect_oracle.py check`检查表是否存在
4. **备份原文件**: 修改前请备份原始SQL文件

## 错误排查

### 常见错误及解决方案:

1. **ORA-00942: 表或视图不存在**
   - 检查schema前缀是否正确
   - 确认用户权限
   - 使用`connect_oracle.py check`验证表存在性

2. **ORA-00911: 无效字符**
   - 检查SQL中是否有HTML实体编码
   - 确认字符编码正确

3. **连接失败**
   - 检查网络连接
   - 确认Oracle Instant Client已安装
   - 验证数据库连接参数

## 工具版本要求

- Python 3.6+
- cx_Oracle 8.0+
- Oracle Instant Client 19c+

## 联系支持

如遇问题，请检查:
1. 数据库连接参数是否正确
2. Oracle客户端是否正确安装
3. 用户权限是否充足
4. 表名和schema名是否正确
