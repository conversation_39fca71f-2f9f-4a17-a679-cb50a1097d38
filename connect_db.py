import psycopg2
from datetime import datetime

def connect_to_database():
    """连接到数据库并返回连接对象"""
    try:
        # 数据库连接参数
        conn_string = "*******************************************/testdb"
        
        # 连接数据库
        conn = psycopg2.connect(conn_string)
        print("数据库连接成功!")
        return conn
    except Exception as e:
        print(f"数据库连接失败: {str(e)}")
        return None

def execute_sql_file(sql_file_path):
    """执行SQL文件"""
    conn = connect_to_database()
    if conn:
        try:
            # 读取SQL文件内容
            with open(sql_file_path, 'r', encoding='utf-8') as file:
                sql_content = file.read()

            print(f"正在执行SQL文件: {sql_file_path}")
            print("SQL内容预览:")
            print(sql_content[:200] + "..." if len(sql_content) > 200 else sql_content)

            # 创建游标
            cur = conn.cursor()

            # 由于这是一个存储过程块，需要包装在DO块中执行
            # 先检查SQL内容是否已经包含完整的块结构
            if sql_content.strip().startswith('DECLARE') and not sql_content.strip().startswith('DO'):
                wrapped_sql = f"""
DO $$
{sql_content}
$$;
"""
                print("已包装为DO块")
            else:
                wrapped_sql = sql_content
                print("使用原始SQL内容")

            print("开始执行SQL...")

            # 执行SQL
            cur.execute(wrapped_sql)

            # 提交事务
            conn.commit()

            print("SQL文件执行成功!")

            # 关闭游标和连接
            cur.close()
            conn.close()
            print("数据库连接已关闭")

        except Exception as e:
            print(f"SQL文件执行失败: {str(e)}")
            import traceback
            print("详细错误信息:")
            print(traceback.format_exc())
            if conn:
                conn.rollback()
                conn.close()

def test_connection():
    """测试数据库连接并执行简单查询"""
    conn = connect_to_database()
    if conn:
        try:
            # 创建游标
            cur = conn.cursor()

            # 执行简单查询
            cur.execute("SELECT current_timestamp, current_database(), current_user")

            # 获取结果
            result = cur.fetchone()
            print("\n数据库连接信息:")
            print(f"当前时间: {result[0]}")
            print(f"当前数据库: {result[1]}")
            print(f"当前用户: {result[2]}")

            # 关闭游标和连接
            cur.close()
            conn.close()
            print("数据库连接已关闭")
        except Exception as e:
            print(f"查询执行失败: {str(e)}")

def verify_tables():
    """验证创建的表是否存在"""
    conn = connect_to_database()
    if conn:
        try:
            cur = conn.cursor()

            # 查询创建的表
            cur.execute("""
                SELECT table_name
                FROM information_schema.tables
                WHERE table_schema = 'dw'
                AND table_name LIKE 'dw_asset_cust_maxbal_pt_%'
                ORDER BY table_name;
            """)

            tables = cur.fetchall()
            print(f"\n成功创建了 {len(tables)} 个表:")
            for table in tables:
                print(f"  - {table[0]}")

            cur.close()
            conn.close()

        except Exception as e:
            print(f"验证表失败: {str(e)}")
            if conn:
                conn.close()

def check_oracle_tables():
    """检查Oracle数据库中SQL涉及的表是否存在"""
    # 从SQL中提取的表名列表
    tables_to_check = [
        'CM_TRADE_NUM_SUMMARY',
        'CM_PRP_PREID_EXP_COEFF',
        'CM_PREBOOKPRODUCTINFO',
        'CM_PREBOOK_MANYCALL',
        'CM_DISCOUNTAPP',
        'CM_CONSULTANT',
        'CM_PRP_PRODUCT_COEFFICIENT',
        'CM_PRP_CUST_SOURCE_COEFF',
        'CM_PRP_SOURCE_COEFFICIENT',
        'JJXX1',
        'RMBHLZJJ',
        'CM_PRP_ORDERINFO_TYPE_COEFF',
        'CM_PRP_TRADE_NUM'
    ]

    # Oracle连接参数 - 需要根据实际情况修改
    try:
        import cx_Oracle
        # 这里需要根据实际的Oracle连接信息修改
        oracle_conn_string = "username/password@hostname:port/service_name"
        print("注意: 请修改Oracle连接字符串")
        print("当前使用PostgreSQL连接检查...")

        conn = connect_to_database()  # 使用现有的PostgreSQL连接
        if conn:
            cur = conn.cursor()

            print("\n检查表/视图是否存在:")
            print("=" * 50)

            missing_tables = []
            existing_tables = []

            for table_name in tables_to_check:
                try:
                    # 检查表是否存在 (PostgreSQL语法)
                    cur.execute("""
                        SELECT EXISTS (
                            SELECT FROM information_schema.tables
                            WHERE table_name = %s
                        );
                    """, (table_name.lower(),))

                    exists = cur.fetchone()[0]

                    if exists:
                        existing_tables.append(table_name)
                        print(f"✅ {table_name} - 存在")
                    else:
                        # 检查是否是视图
                        cur.execute("""
                            SELECT EXISTS (
                                SELECT FROM information_schema.views
                                WHERE table_name = %s
                            );
                        """, (table_name.lower(),))

                        is_view = cur.fetchone()[0]
                        if is_view:
                            existing_tables.append(table_name)
                            print(f"✅ {table_name} - 存在 (视图)")
                        else:
                            missing_tables.append(table_name)
                            print(f"❌ {table_name} - 不存在")

                except Exception as e:
                    missing_tables.append(table_name)
                    print(f"❌ {table_name} - 检查失败: {str(e)}")

            print("\n" + "=" * 50)
            print(f"总计: {len(existing_tables)} 个存在, {len(missing_tables)} 个不存在")

            if missing_tables:
                print(f"\n缺失的表/视图:")
                for table in missing_tables:
                    print(f"  - {table}")

            cur.close()
            conn.close()

    except ImportError:
        print("未安装cx_Oracle，使用PostgreSQL连接检查...")
        # 使用PostgreSQL连接检查
        conn = connect_to_database()
        if conn:
            cur = conn.cursor()

            print("\n检查表/视图是否存在 (PostgreSQL):")
            print("=" * 50)

            missing_tables = []
            existing_tables = []

            for table_name in tables_to_check:
                try:
                    # 检查表是否存在 (PostgreSQL语法)
                    cur.execute("""
                        SELECT EXISTS (
                            SELECT FROM information_schema.tables
                            WHERE UPPER(table_name) = UPPER(%s)
                        );
                    """, (table_name,))

                    exists = cur.fetchone()[0]

                    if exists:
                        existing_tables.append(table_name)
                        print(f"✅ {table_name} - 存在")
                    else:
                        # 检查是否是视图
                        cur.execute("""
                            SELECT EXISTS (
                                SELECT FROM information_schema.views
                                WHERE UPPER(table_name) = UPPER(%s)
                            );
                        """, (table_name,))

                        is_view = cur.fetchone()[0]
                        if is_view:
                            existing_tables.append(table_name)
                            print(f"✅ {table_name} - 存在 (视图)")
                        else:
                            missing_tables.append(table_name)
                            print(f"❌ {table_name} - 不存在")

                except Exception as e:
                    missing_tables.append(table_name)
                    print(f"❌ {table_name} - 检查失败: {str(e)}")

            print("\n" + "=" * 50)
            print(f"总计: {len(existing_tables)} 个存在, {len(missing_tables)} 个不存在")

            if missing_tables:
                print(f"\n缺失的表/视图:")
                for table in missing_tables:
                    print(f"  - {table}")

            print(f"\n注意: 当前连接的是PostgreSQL数据库")
            print(f"如果您的应用使用Oracle数据库，请检查Oracle数据库中的表")

            cur.close()
            conn.close()
    except Exception as e:
        print(f"检查表失败: {str(e)}")

if __name__ == "__main__":
    # 可以选择测试连接或执行SQL文件
    import sys

    if len(sys.argv) > 1 and sys.argv[1] == "execute":
        # 执行create_table_tmp.sql文件
        execute_sql_file("create_table_tmp.sql")
    elif len(sys.argv) > 1 and sys.argv[1] == "verify":
        # 验证创建的表
        verify_tables()
    elif len(sys.argv) > 1 and sys.argv[1] == "check":
        # 检查Oracle SQL中的表是否存在
        check_oracle_tables()
    else:
        # 默认测试连接
        test_connection()
