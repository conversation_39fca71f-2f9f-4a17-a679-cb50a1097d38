import psycopg2
from datetime import datetime

def connect_to_database():
    """连接到数据库并返回连接对象"""
    try:
        # 数据库连接参数
        conn_string = "*******************************************/testdb"
        
        # 连接数据库
        conn = psycopg2.connect(conn_string)
        print("数据库连接成功!")
        return conn
    except Exception as e:
        print(f"数据库连接失败: {str(e)}")
        return None

def execute_sql_file(sql_file_path):
    """执行SQL文件"""
    conn = connect_to_database()
    if conn:
        try:
            # 读取SQL文件内容
            with open(sql_file_path, 'r', encoding='utf-8') as file:
                sql_content = file.read()

            print(f"正在执行SQL文件: {sql_file_path}")
            print("SQL内容预览:")
            print(sql_content[:200] + "..." if len(sql_content) > 200 else sql_content)

            # 创建游标
            cur = conn.cursor()

            # 由于这是一个存储过程块，需要包装在DO块中执行
            # 先检查SQL内容是否已经包含完整的块结构
            if sql_content.strip().startswith('DECLARE') and not sql_content.strip().startswith('DO'):
                wrapped_sql = f"""
DO $$
{sql_content}
$$;
"""
                print("已包装为DO块")
            else:
                wrapped_sql = sql_content
                print("使用原始SQL内容")

            print("开始执行SQL...")

            # 执行SQL
            cur.execute(wrapped_sql)

            # 提交事务
            conn.commit()

            print("SQL文件执行成功!")

            # 关闭游标和连接
            cur.close()
            conn.close()
            print("数据库连接已关闭")

        except Exception as e:
            print(f"SQL文件执行失败: {str(e)}")
            import traceback
            print("详细错误信息:")
            print(traceback.format_exc())
            if conn:
                conn.rollback()
                conn.close()

def test_connection():
    """测试数据库连接并执行简单查询"""
    conn = connect_to_database()
    if conn:
        try:
            # 创建游标
            cur = conn.cursor()

            # 执行简单查询
            cur.execute("SELECT current_timestamp, current_database(), current_user")

            # 获取结果
            result = cur.fetchone()
            print("\n数据库连接信息:")
            print(f"当前时间: {result[0]}")
            print(f"当前数据库: {result[1]}")
            print(f"当前用户: {result[2]}")

            # 关闭游标和连接
            cur.close()
            conn.close()
            print("数据库连接已关闭")
        except Exception as e:
            print(f"查询执行失败: {str(e)}")

def verify_tables():
    """验证创建的表是否存在"""
    conn = connect_to_database()
    if conn:
        try:
            cur = conn.cursor()

            # 查询创建的表
            cur.execute("""
                SELECT table_name
                FROM information_schema.tables
                WHERE table_schema = 'dw'
                AND table_name LIKE 'dw_asset_cust_maxbal_pt_%'
                ORDER BY table_name;
            """)

            tables = cur.fetchall()
            print(f"\n成功创建了 {len(tables)} 个表:")
            for table in tables:
                print(f"  - {table[0]}")

            cur.close()
            conn.close()

        except Exception as e:
            print(f"验证表失败: {str(e)}")
            if conn:
                conn.close()

if __name__ == "__main__":
    # 可以选择测试连接或执行SQL文件
    import sys

    if len(sys.argv) > 1 and sys.argv[1] == "execute":
        # 执行create_table_tmp.sql文件
        execute_sql_file("create_table_tmp.sql")
    elif len(sys.argv) > 1 and sys.argv[1] == "verify":
        # 验证创建的表
        verify_tables()
    else:
        # 默认测试连接
        test_connection()
