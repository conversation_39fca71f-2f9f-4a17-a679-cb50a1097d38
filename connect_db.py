import psycopg2
from datetime import datetime

def connect_to_database():
    """连接到数据库并返回连接对象"""
    try:
        # 数据库连接参数
        conn_string = "*******************************************/testdb"
        
        # 连接数据库
        conn = psycopg2.connect(conn_string)
        print("数据库连接成功!")
        return conn
    except Exception as e:
        print(f"数据库连接失败: {str(e)}")
        return None

def test_connection():
    """测试数据库连接并执行简单查询"""
    conn = connect_to_database()
    if conn:
        try:
            # 创建游标
            cur = conn.cursor()
            
            # 执行简单查询
            cur.execute("SELECT current_timestamp, current_database(), current_user")
            
            # 获取结果
            result = cur.fetchone()
            print("\n数据库连接信息:")
            print(f"当前时间: {result[0]}")
            print(f"当前数据库: {result[1]}")
            print(f"当前用户: {result[2]}")
            
            # 关闭游标和连接
            cur.close()
            conn.close()
            print("数据库连接已关闭")
        except Exception as e:
            print(f"查询执行失败: {str(e)}")

if __name__ == "__main__":
    test_connection()
